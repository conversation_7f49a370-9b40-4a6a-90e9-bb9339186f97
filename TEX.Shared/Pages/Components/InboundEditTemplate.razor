@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Microsoft.JSInterop;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;

<div style="white-space: nowrap; margin:16px 0;">
    <div class="@parentTableClass">
        <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"   IsExcel="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" IsTracking="true"
               OnDeleteAsync="@OnDeleteAsync" ShowToastAfterSaveOrDeleteModel="false"
               ShowEditButton="false" ShowDeleteButton="true" 
               IsMultipleSelect="false" ShowAddButton="@isAdd"
               ShowLineNo="true" LineNoText="序号" class="footer-demo"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true" ShowExtendDeleteButton="false"
               IsFixedHeader="true" ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true"  >
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="AllOrderDetails" />
                
                <TableColumn @bind-Field="@context.LotNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.LotNo" FormatString="0" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Location" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Location" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductInboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
            </RowButtonTemplate>

            <FooterTemplate>
                <TableFooterCell />
                <TableFooterCell Text="合计:" style="height: 36px;"/>
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductInboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Yards)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>


        </Table>
    </div>
    <div class="@childTableClass">
        @*EnableKeyboardNavigationCell="true"在bb9.0以上版本有,但是并不好用,数字列中上下键依然只能+-1,无法移动焦点*@
        <Table @bind-Items="rollDetailList" TItem="ProductInboundRoll" 
               IsExcel="true" OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" OnDeleteAsync="@OnDeleteRollAsync"
               ShowToolbar="true" ShowDeleteButton="true" ShowRefresh="false"
               ShowExtendButtons="false" IsTracking="true"
               ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact"  
               IsFixedHeader="true"  IsBordered="true"
               ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.RollNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.RollNo" FormatString="0" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnRollEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnRollEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
            </TableToolbarTemplate> 
            <FooterTemplate>
                @* <TableFooterCell Text="合计:" style="height: 36px;" /> *@
                <TableFooterCell style="height: 36px;" Text="合计:" Aggregate="AggregateType.Count" Field="@nameof(ProductInboundRoll.RollNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Yards)" />
                <TableFooterCell  />
            </FooterTemplate>
        </Table>

    </div>
</div>

<style>
    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
        vertical-align: top;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
        vertical-align: top;
    }

    .footer-demo hr {
        margin: 0;
    }

    .footer-demo tfoot tr,
    .footer-demo .table-row.table-footer .table-cell {
        color: #409eff;
        font-weight: bold;
    }
</style>

<script>
    // Enter键导航到下一行 - 简化版本，不依赖ID
    window.navigateToNextRow = function (currentInput) {
        try {
            const td = currentInput.closest('td');
            const tr = td.parentNode;
            const nextRow = tr.nextElementSibling;

            if (nextRow) {
                // 获取当前单元格在行中的索引
                const cells = [...tr.children];
                const currentIndex = cells.indexOf(td);
                const nextRowCells = [...nextRow.children];

                // 在下一行的相同列位置查找输入框
                if (nextRowCells[currentIndex]) {
                    const nextInput = nextRowCells[currentIndex].querySelector('input');
                    if (nextInput && !nextInput.disabled && !nextInput.readOnly) {
                        nextInput.focus();
                        if (nextInput.select) {
                            nextInput.select();
                        }
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.error('Error navigating to next row:', error);
            return false;
        }
    };
</script>
@code {

    [Parameter]
    public ProductInboundBill Bill { get; set; } = new();
    [Parameter]
    public EventCallback<ProductInboundBill> BillChanged { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();

    ProductInboundLot SelectedLot { get; set; }//存储当前选择的Lot
    private bool isAdd = false;
    //Lot子表绑定数据
    private IEnumerable<ProductInboundLot> DetailList
    {
        get => Bill.LotList;
        set => Bill.LotList = value.ToList();
    }

    //Roll孙表绑定数据
    List<ProductInboundRoll> RollDetailList = new List<ProductInboundRoll>();
    public IEnumerable<ProductInboundRoll> rollDetailList
    {
        get => RollDetailList;
        set => RollDetailList = value.ToList();
    }


    protected override async Task OnInitializedAsync()
    {
        Bill.LotList ??= [];

        await base.OnInitializedAsync();
    }

    //当选择订单后,刷新订单明细选择数据源
    protected override async Task OnParametersSetAsync()
    {
        if (Bill.POrderId != Guid.Empty)
        {
            await SearchOrderDetailAsync(Bill.POrderId);
            isAdd = true;
        }
    }

    private async Task SearchOrderDetailAsync(Guid id)
    {
        var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/Models/OrderDetail/GetOrderDetailSelectListItemsByPurchaseOrderId/{id}");
        AllOrderDetails = rv.Data;
    }



    #region 更新Lot
    //Lot子表Excel模式,更新方法
    private async Task<ProductInboundLot> OnAddAsync()
    {
        var od = new ProductInboundLot();
        Bill.LotList ??= [];
        od.ID = Guid.NewGuid();
        Bill.LotList.Insert(Bill.LotList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundLot item, ItemChangedType changedType)
    {
        Bill.Pcs = Bill.LotList.Count;
        Bill.Weight = Bill.LotList.Sum(x => x.Weight);
        Bill.Meters = Bill.LotList.Sum(x => x.Meters);
        Bill.Yards = Bill.LotList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundLot> items)
    {
        Bill.LotList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    #endregion


    #region 更新Roll
    //更新Roll
    private async Task<ProductInboundRoll> OnAddRollAsync()
    {
        var od = new ProductInboundRoll();
        RollDetailList ??= new();
        od.ID = Guid.NewGuid();
        od.RollNo = RollDetailList.Count + 1;
        RollDetailList.Insert(RollDetailList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveRollAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // Bill.Pcs = RollDetailList.Count();
        // Bill.Weight = RollDetailList.Sum(x => x.Weight);
        // Bill.Meters = RollDetailList.Sum(x => x.Meters);
        // Bill.Yards = RollDetailList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteRollAsync(IEnumerable<ProductInboundRoll> items)
    {
        RollDetailList.RemoveAll(i => items.ToList().Contains(i));
        return Task.FromResult(true);
    }
    private string parentTableClass = "parent-table-normal";
    private string childTableClass = "child-table-hidden";
    //行明细按钮点击控制显示Roll表格
    private void OnDetailsClick(ProductInboundLot item)
    {
        // 检查是否点击的是同一行
        if (SelectedLot is not null && SelectedLot.ID == item.ID)
        {
            // 点击同一行时，切换显示状态
            parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
            childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";
        }
        else
        {
            // 点击不同行时，显示新行的数据
            SelectedLot = item;
            RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

            // 确保明细表格显示
            parentTableClass = "parent-table-compressed";
            childTableClass = "child-table-visible";
        }
        StateHasChanged();
    }

    //保存Roll
    private async Task SaveRolls()
    {
        var list = new List<ProductInboundRoll>();

        //去除空白Roll
        foreach (var item in RollDetailList)
        {
            if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
            {
                list.Add(item);
            }
        }
        SelectedLot.RollList = list;
        SelectedLot.Color = AllOrderDetails.FirstOrDefault(x => x.Value == SelectedLot.OrderDetailId.ToString())?.Text;
        SelectedLot.Pcs = SelectedLot.RollList.Count;
        SelectedLot.Weight = SelectedLot.RollList.Sum(x => x.Weight);
        SelectedLot.Meters = SelectedLot.RollList.Sum(x => x.Meters);
        SelectedLot.Yards = SelectedLot.RollList.Sum(x => x.Yards);
        int index = Bill.LotList.FindIndex(x => x.ID == SelectedLot.ID);
        Bill.LotList[index] = SelectedLot;
        // 保存后隐藏明细表格
        parentTableClass = "parent-table-normal";
        childTableClass = "child-table-hidden";
        await Task.CompletedTask;
        //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
    }
    #endregion

    // Lot表格Enter键处理 - 简化版本，不依赖ID
    private async Task OnLotEnterAsync()
    {
        try
        {
            // 先尝试使用JavaScript导航到下一行
            await JSRuntime.InvokeVoidAsync("eval", @"
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.tagName === 'INPUT') {
                        const result = window.navigateToNextRow(activeElement);
                        if (!result) {
                            // 如果导航失败，触发添加新行
                            window.blazorAddNewLotRow = true;
                        }
                    }
                })();
            ");

            // 检查是否需要添加新行
            var needNewRow = await JSRuntime.InvokeAsync<bool>("eval", "window.blazorAddNewLotRow || false");
            if (needNewRow)
            {
                await JSRuntime.InvokeVoidAsync("eval", "window.blazorAddNewLotRow = false;");
                await OnAddAsync(); // 添加新行
                StateHasChanged(); // 强制重新渲染
                await Task.Delay(50); // 等待DOM更新完成

                // 再次尝试导航
                await JSRuntime.InvokeVoidAsync("eval", @"
                    (function() {
                        const activeElement = document.activeElement;
                        if (activeElement && activeElement.tagName === 'INPUT') {
                            window.navigateToNextRow(activeElement);
                        }
                    })();
                ");
            }
        }
        catch (Exception ex)
        {
            // 如果JavaScript导航失败，回退到添加新行
            System.Console.WriteLine($"Enter navigation failed: {ex.Message}");
            await OnAddAsync();
            StateHasChanged();
        }
    }

    // Roll表格Enter键处理 - 简化版本，不依赖ID
    private async Task OnRollEnterAsync()
    {
        try
        {
            // 先尝试使用JavaScript导航到下一行
            await JSRuntime.InvokeVoidAsync("eval", @"
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.tagName === 'INPUT') {
                        const result = window.navigateToNextRow(activeElement);
                        if (!result) {
                            // 如果导航失败，触发添加新行
                            window.blazorAddNewRollRow = true;
                        }
                    }
                })();
            ");

            // 检查是否需要添加新行
            var needNewRow = await JSRuntime.InvokeAsync<bool>("eval", "window.blazorAddNewRollRow || false");
            if (needNewRow)
            {
                await JSRuntime.InvokeVoidAsync("eval", "window.blazorAddNewRollRow = false;");
                await OnAddRollAsync(); // 添加新行
                StateHasChanged(); // 强制重新渲染
                await Task.Delay(50); // 等待DOM更新完成

                // 再次尝试导航
                await JSRuntime.InvokeVoidAsync("eval", @"
                    (function() {
                        const activeElement = document.activeElement;
                        if (activeElement && activeElement.tagName === 'INPUT') {
                            window.navigateToNextRow(activeElement);
                        }
                    })();
                ");
            }
        }
        catch (Exception ex)
        {
            // 如果JavaScript导航失败，回退到添加新行
            System.Console.WriteLine($"Enter navigation failed: {ex.Message}");
            await OnAddRollAsync();
            StateHasChanged();
        }
    }
}